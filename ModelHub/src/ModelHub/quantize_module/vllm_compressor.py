from datasets import load_dataset
from llmcompressor.entrypoints import Oneshot
from llmcompressor.entrypoints.utils import post_process
from llmcompressor.modifiers.quantization import GPTQModifier
from llmcompressor.modifiers.smoothquant import SmoothQuantModifier

from ModelHub.data_module.utils.prepare_calibration_data import prepare_calibration_data


MAX_SEQUENCE_LENGTH = 2048


def main(model_wrapper, dataset_name=None):
    model = model_wrapper.model
    processor = model_wrapper.processor

    if dataset_name is not None:
        # Prepare calibration data
        dataloader = prepare_calibration_data(
            processor=processor, num_samples=512, batch_size=1, dataset_name=dataset_name
        )
    else:
        # Prepare base calibration data
        DATASET_ID = "HuggingFaceH4/ultrachat_200k"
        DATASET_SPLIT = "train_sft"
        NUM_CALIBRATION_SAMPLES = 512

        calibration_dataset = load_dataset(DATASET_ID, split=f"{DATASET_SPLIT}[:{NUM_CALIBRATION_SAMPLES}]")
        calibration_dataset = calibration_dataset.shuffle(seed=42)
        dataloader = [
            processor.prepare_inputs(text["prompt"], [], return_tensors="pt") for text in calibration_dataset
        ]

    recipe = [
        SmoothQuantModifier(smoothing_strength=0.8),
        GPTQModifier(targets="Linear", scheme="W8A8", ignore=["lm_head", "re:model.visual.*"]),
    ]

    one_shot = Oneshot(
        model=model,
        dataset=None,
        recipe=recipe,
        max_seq_length=MAX_SEQUENCE_LENGTH,
        num_calibration_samples=NUM_CALIBRATION_SAMPLES,
        sequential_targets=["Qwen2_5_VLDecoderLayer"],
    )

    # Retrieved from __call__ of Oneshot class
    one_shot.apply_recipe_modifiers(
        calibration_dataloader=dataloader,
        recipe_stage=one_shot.recipe_args.stage,
    )
    post_process(
        model_args=one_shot.model_args,
        recipe_args=one_shot.recipe_args,
        output_dir=one_shot.output_dir,
    )

    # Save to disk compressed.
    SAVE_DIR = model_wrapper.model_name.rstrip("/").split("/")[-1] + "-W8A8-Dynamic-Per-Token"
    model.save_pretrained(SAVE_DIR, save_compressed=True)
    processor.tokenizer.save_pretrained(SAVE_DIR)


if __name__ == "__main__":
    from ModelHub.models.VLM.Qwen.qwen2_vl.eval_wrapper import Qwen2VL

    model_wrapper = Qwen2VL()
    main(model_wrapper)
