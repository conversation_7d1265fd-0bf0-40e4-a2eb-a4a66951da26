from types import MethodType

import numpy as np
import torch
from huggingface_hub import snapshot_download
from transformers import (
    Qwen2_5_VLForConditionalGeneration,
    Qwen2_5_VLProcessor,
    Qwen2VLForConditionalGeneration,
    Qwen2VLProcessor,
)

from ModelHub.data_module.conversation import get_conv_template
from ModelHub.data_module.utils.prepare_inputs import complete_inputs_preparation
from ModelHub.models import CHECKPOINT_DIR
from ModelHub.train_module.args import DataArguments
from ModelHub.train_module.utils.base_utils import visualize_labels

from .utils import process_video_info


def load_processor_and_model(model_name, load_processor_only=False):
    local_dir = f"{CHECKPOINT_DIR}/{model_name.split('/')[-1]}"

    # Download model from Hugging Face Hub
    snapshot_download(model_name, local_dir=local_dir)

    # Load model and processor
    if "Qwen2-VL" in model_name:
        processor = Qwen2VLProcessor.from_pretrained(local_dir)
    elif "Qwen2.5-VL" in model_name:
        processor = Qwen2_5_VLProcessor.from_pretrained(local_dir)
    else:
        raise ValueError(f"Invalid model name: {model_name}")

    # Restrict image size to 3M pixels to prevent OOM errors
    processor.image_processor.max_pixels = 3 * 1024 * 1024

    # Set required attributes for the processor
    processor.model_max_length = 8192
    processor.preprocess_conv_fn = preprocess_qwen
    processor.conv_template = get_conv_template("qwen")
    processor.make_prompt = MethodType(make_prompt, processor)
    processor.prepare_inputs = MethodType(prepare_inputs, processor)
    processor.custom_batch_fn = custom_batch_fn
    processor.tokenizer.skip_sanity_check = True

    if load_processor_only:
        return processor

    # Load model
    if "Qwen2-VL" in model_name:
        model = Qwen2VLForConditionalGeneration.from_pretrained(
            local_dir, torch_dtype=torch.bfloat16, device_map="auto"
        )
    elif "Qwen2.5-VL" in model_name:
        model = Qwen2_5_VLForConditionalGeneration.from_pretrained(
            local_dir, torch_dtype=torch.bfloat16, device_map="auto"
        )
    else:
        raise ValueError(f"Invalid model name: {model_name}")

    # Set generation config to remove unnecessary warnings
    model.generation_config.top_k = None
    model.generation_config.top_p = None
    model.generation_config.temperature = None
    model.generation_config.do_sample = True

    return model, processor


def _fill_content(content, **kwargs):
    video_path = kwargs["video_path"]
    nframes = kwargs.get("nframes", None)
    if nframes is None:
        # If nframe is not specified, it will be automatically calculated in smart_nframes()
        content.append({"video": video_path})
    else:
        content.append({"video": video_path, "nframes": nframes})
    return content


def make_prompt(self, question, num_images: int, **kwargs):
    content = []
    for _ in range(num_images):
        content.append({"type": "image", "image": "tmp.jpeg"})
    content.append({"type": "text", "text": question})

    if kwargs.get("video_path", None) is not None:
        content = _fill_content(content, **kwargs)

    # Preparation for inference
    messages = [{"role": "user", "content": content}]
    prompt = self.apply_chat_template(messages, tokenize=False, add_generation_prompt=True)

    """
    <image> token is not needed originally in the prompt before self.apply_chat_template.
    But when <image> exists in the prompt, regardless of its existence in the content,
    "<|vision_start|><|image_pad|><|vision_end|>" will be added to the prompt.
    So we first remove the added "<|vision_start|><|image_pad|><|vision_end|>" token in the prompt.
    Then we replace the existing <image> token with "<|vision_start|><|image_pad|><|vision_end|>".
    """
    num_excess_img_tokens = prompt.count("<image>")
    assert num_excess_img_tokens == num_images or num_excess_img_tokens == 0, (
        "The number of <image> tokens in the prompt must be equal to num_images or 0."
    )
    if num_excess_img_tokens > 0:
        prompt = prompt.replace("<|vision_start|><|image_pad|><|vision_end|>", "")
        prompt = prompt.replace("<image>", "<|vision_start|><|image_pad|><|vision_end|>", num_excess_img_tokens)

    return prompt


def prepare_inputs(self, text, image_list, return_fixed_text=False, **kwargs):
    """
    Args:
        text: str
        image_list: List[Image]
        **kwargs: It should be arguments for tokenizer
    """
    if kwargs.get("video_path", None) is not None:
        content = _fill_content([], **kwargs)
        video_list, video_kwargs = process_video_info([{"role": "user", "content": content}], return_video_kwargs=True)
    else:
        video_list = [None]
        video_kwargs = {}

    if len(image_list) == 0:
        image_list = None

    inputs = self.__call__(
        text=text, images=image_list, videos=video_list[0], padding=True, return_tensors="pt", **video_kwargs
    )

    if return_fixed_text:
        return inputs, self.tokenizer.decode(inputs.input_ids[0])

    return inputs


def preprocess_qwen(sources, image_list, processor, data_args: DataArguments):
    conv = processor.conv_template.copy()
    roles = {"human": conv.roles[0], "gpt": conv.roles[1]}

    conversations = []
    for i, source in enumerate(sources):
        if roles[source[0]["from"]] != conv.roles[0]:
            # Skip the first one if it is not from human
            source = source[1:]

        conv.messages = []
        for j, sentence in enumerate(source):
            role = roles[sentence["from"]]
            assert role == conv.roles[j % 2], f"{i}"
            sentence["value"] = sentence["value"].strip()
            sentence["value"] = sentence["value"].replace("<image>", "<|vision_start|><|image_pad|><|vision_end|>")
            conv.append_message(role, sentence["value"])
        conversations.append(conv.get_prompt())

    # Tokenize conversations
    inputs, conversations = processor.prepare_inputs(
        conversations, image_list, return_fixed_text=True, return_tensors="pt", padding="longest", truncation=True
    )
    input_ids = inputs.input_ids.squeeze(0)

    # Mask targets
    tokens = processor.tokenizer.convert_ids_to_tokens(input_ids, skip_special_tokens=False)
    targets = input_ids.clone()

    # "<|im_start|>assistant\n" is start of non-masking tokens and the following "<|im_end|>\n" is the end.
    flags = processor.tokenizer.encode("<|im_start|>assistant\n", add_special_tokens=False)

    # Compute pivot position where model starts to generate
    start_pivot = (input_ids == flags[0]).nonzero().squeeze() + 1
    model_pivot = (input_ids == flags[1]).nonzero().squeeze()
    pivot = torch.Tensor(np.intersect1d(start_pivot.numpy(), model_pivot.numpy())).long() + 1
    new_line_pivot = (input_ids == flags[2]).nonzero().squeeze()
    pivot = torch.Tensor(np.intersect1d(pivot.numpy(), new_line_pivot.numpy())).long() + 1

    # Compute label mask based on the pivot
    label_mask = []
    masking = True
    for i, token in enumerate(tokens):
        if i in pivot:
            masking = False

        elif token == "<|im_end|>" and not masking:
            label_mask.append(False)
            masking = True
            continue  # Skip adding label for <|im_end|> again

        if masking:
            label_mask.append(True)
        else:
            label_mask.append(False)

    targets[torch.Tensor(label_mask).bool()] = data_args.image_ignore_index
    if False:  # Inspect and check the correctness of masking
        processor.tokenizer.unk_token_id = processor.tokenizer.pad_token_ids
        visualize_labels(targets, processor, data_args)

    processed_inputs = {"input_ids": input_ids, "labels": targets, "original_text": conversations}
    return complete_inputs_preparation(inputs, processed_inputs)


def custom_batch_fn(batch, instances, processor, data_args):
    padding_side = "right"
    if any(cur_input_ids[0] == processor.tokenizer.pad_token_id for cur_input_ids in batch["input_ids"]):
        padding_side = "left"

    batch_input_ids = []
    batch_label_ids = []
    batch_pixel_values = []
    batch_pixel_video_values = []
    batch_video_thw = []
    batch_image_thw = []
    batch_second_per_grid_ts = []

    for example in instances:
        # Length truncation for Qwen should be applied after last vision token
        input_ids = example["input_ids"]
        if 151655 in input_ids:  # Image token
            last_vision_token_index = (input_ids == 151655).nonzero()[-1].item()
        elif 151656 in input_ids:  # Video token
            last_vision_token_index = (input_ids == 151656).nonzero()[-1].item()
        else:
            last_vision_token_index = 0
        max_len = processor.model_max_length + last_vision_token_index

        batch_input_ids.append(example["input_ids"][:max_len])
        if batch["labels"] is not None:
            batch_label_ids.append(example["labels"][:max_len])

        keys = example.keys()
        if "pixel_values_videos" in keys:
            batch_pixel_video_values.append(example["pixel_values_videos"])
            batch_video_thw.append(example["video_grid_thw"])
        elif "pixel_values" in keys:
            batch_pixel_values.append(example["pixel_values"])
            batch_image_thw.append(example["image_grid_thw"])

        if "second_per_grid_ts" in keys:
            batch_second_per_grid_ts.extend(example["second_per_grid_ts"])

    batch["input_ids"] = torch.nn.utils.rnn.pad_sequence(
        batch_input_ids,
        batch_first=True,
        padding_value=processor.tokenizer.pad_token_id,
        padding_side=padding_side,
    )
    batch["attention_mask"] = batch["input_ids"].ne(processor.tokenizer.pad_token_id)

    if batch["labels"] is not None:
        batch["labels"] = torch.nn.utils.rnn.pad_sequence(
            batch_label_ids, batch_first=True, padding_side=padding_side, padding_value=-100
        )

    if len(batch_pixel_values) > 0:
        batch["pixel_values"] = torch.cat(batch_pixel_values, dim=0)
        batch["image_grid_thw"] = torch.cat(batch_image_thw, dim=0)

    if len(batch_pixel_video_values) > 0:
        batch["pixel_values_videos"] = torch.cat(batch_pixel_video_values, dim=0)
        batch["video_grid_thw"] = torch.cat(batch_video_thw, dim=0)

    if len(batch_second_per_grid_ts) > 0:
        batch["second_per_grid_ts"] = batch_second_per_grid_ts

    return batch
