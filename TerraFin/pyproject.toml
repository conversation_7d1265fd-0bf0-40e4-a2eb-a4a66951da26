[project]
name = "TerraFin"
version = "0.0.1"
description = "A comprehensive toolkit for financial data handling, engineering methods, and visualization."
authors = [
    {name = "Ki-<PERSON>g Song", email = "<EMAIL>"}
]
readme = "README.md"
license = { text = "MIT" }
classifiers = [
    "Programming Language :: Python :: 3.11",
    "License :: OSI Approved :: MIT License",
    "Operating System :: OS Independent",
    "Intended Audience :: Developers",
    "Intended Audience :: Financial and Insurance Industry",
    "Intended Audience :: Science/Research",
    "Topic :: Office/Business :: Financial",
    "Topic :: Scientific/Engineering :: Information Analysis",
    "Topic :: Software Development :: Libraries :: Python Modules"
]
dependencies = [
    "yfinance",
    "plotly",
    "arch",
    "lightweight-charts",
    "python-dotenv",
    "sec-parser",
    "pyrate-limiter",
    "cachetools",
    "requests",
    "beautifulsoup4",
    "numpy",
    "fastapi",
    "uvicorn[standard]",
    "websockets"
]

[build-system]
requires = ["setuptools>=61.0.0", "wheel"] # Pinning setuptools for robustness
build-backend = "setuptools.build_meta"

[tool.setuptools]
package-dir = {"" = "src"}
packages = {find = {where = ["src"], namespaces = false}}

[tool.ruff]
line-length = 119

[tool.ruff.lint]
# Never enforce `E501` (line length violations).
ignore = ["C901", "E501", "E741", "F402", "F823" ]
select = ["C", "E", "F", "I", "W"]

# Ignore import violations in all `__init__.py` files.
[tool.ruff.lint.per-file-ignores]
"__init__.py" = ["E402", "F401", "F403", "F811"]

[tool.ruff.lint.isort]
lines-after-imports = 2

[tool.ruff.format]
# Like Black, use double quotes for strings.
quote-style = "double"

# Like Black, indent with spaces, rather than tabs.
indent-style = "space"

# Like Black, respect magic trailing commas.
skip-magic-trailing-comma = false

# Like Black, automatically detect the appropriate line ending.
line-ending = "auto"
