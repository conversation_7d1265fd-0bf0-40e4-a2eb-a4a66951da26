#!/usr/bin/env python3
"""
Test script to verify the TerraFinChart fix works with actual data.
"""

import sys
import os
import pandas as pd
import numpy as np
from datetime import datetime, timedelta

# Add the src directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def create_sample_data():
    """Create sample OHLCV data for testing."""
    dates = pd.date_range(start=datetime.now() - timedelta(days=100), periods=100, freq='D')
    
    # Generate realistic price data
    np.random.seed(42)
    price = 100
    data = []
    
    for date in dates:
        # Random walk for price
        change = np.random.normal(0, 2)
        price += change
        
        open_price = price + np.random.normal(0, 0.5)
        high = max(open_price, price) + abs(np.random.normal(0, 1))
        low = min(open_price, price) - abs(np.random.normal(0, 1))
        volume = np.random.randint(1000, 10000)
        
        data.append({
            'time': date.strftime('%Y-%m-%d'),
            'open': round(open_price, 2),
            'high': round(high, 2),
            'low': round(low, 2),
            'close': round(price, 2),
            'volume': volume
        })
    
    return pd.DataFrame(data)

def test_chart():
    """Test the chart with sample data."""
    try:
        from TerraFin.visualization.lightweight_chart import TerraFinChart
        print("✓ Successfully imported TerraFinChart")
    except ImportError as e:
        print(f"✗ Import failed: {e}")
        return False
    
    try:
        # Create sample data
        df = create_sample_data()
        print(f"✓ Created sample data with {len(df)} rows")
        
        # Create chart
        chart = TerraFinChart(
            width=1200,
            height=800,
            title="Test Chart - Fixed Implementation",
            debug=True
        )
        print("✓ Chart created successfully")
        
        # Set data
        chart.set(df)
        print("✓ Data set on chart")
        
        # Show chart
        print("Opening chart in browser...")
        print("Check if the chart displays properly with candlestick data")
        print("Press Ctrl+C to exit")
        
        chart.show(block=True)
        
    except KeyboardInterrupt:
        print("\nTest completed.")
        return True
    except Exception as e:
        print(f"✗ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("TerraFin Chart Fix Test")
    print("=" * 30)
    
    success = test_chart()
    if success:
        print("✓ Test completed successfully")
    else:
        print("✗ Test failed")
        sys.exit(1)
