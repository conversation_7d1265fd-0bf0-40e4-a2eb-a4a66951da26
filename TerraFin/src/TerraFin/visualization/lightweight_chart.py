import asyncio
import json
import threading
import uuid
import webbrowser
from pathlib import Path
from queue import Queue
from typing import Dict, Optional

import uvicorn
from fastapi import FastAPI, WebSocket, WebSocketDisconnect
from fastapi.responses import HTMLResponse

from lightweight_charts import abstract
from lightweight_charts.util import FLOAT, parse_event_message


"""
New Architecture (Option 1: Web Server + WebSocket):
Python Code → WebServerHandler → FastAPI → WebSocket → Browser Client
This eliminates the pywebview dependency while maintaining full interactivity.
"""


class WebSocketManager:
    """Manages WebSocket connections and message routing."""

    def __init__(self):
        self.active_connections: Dict[str, WebSocket] = {}
        self.message_queues: Dict[str, Queue] = {}

    async def connect(self, websocket: WebSocket, client_id: str):
        await websocket.accept()
        self.active_connections[client_id] = websocket
        self.message_queues[client_id] = Queue()

    def disconnect(self, client_id: str):
        if client_id in self.active_connections:
            del self.active_connections[client_id]
        if client_id in self.message_queues:
            del self.message_queues[client_id]

    async def send_script(self, client_id: str, script: str):
        if client_id in self.active_connections:
            try:
                await self.active_connections[client_id].send_text(
                    json.dumps({"type": "execute_script", "script": script})
                )
            except Exception as e:
                print(f"Error sending script to {client_id}: {e}")

    def add_callback_message(self, client_id: str, message: str):
        if client_id in self.message_queues:
            self.message_queues[client_id].put(message)


class WebServerHandler:
    """
    Manages web server and WebSocket connections for chart interaction.
    Replaces pywebview with a web-based solution.
    """

    def __init__(self):
        self.app = FastAPI()
        self.websocket_manager = WebSocketManager()
        self.server_thread: Optional[threading.Thread] = None
        self.server_started = threading.Event()
        self.debug = False
        self.host = "localhost"
        self.port = 8000
        self.client_sessions: Dict[str, dict] = {}

        self._setup_routes()

    def _setup_routes(self):
        """Set up FastAPI routes for serving the chart and handling WebSockets."""

        @self.app.get("/")
        async def serve_chart():
            return HTMLResponse(self._get_chart_html())

        @self.app.websocket("/ws/{client_id}")
        async def websocket_endpoint(websocket: WebSocket, client_id: str):
            await self.websocket_manager.connect(websocket, client_id)
            try:
                while True:
                    data = await websocket.receive_text()
                    message_data = json.loads(data)

                    if message_data.get("type") == "callback":
                        # Handle JavaScript -> Python callbacks
                        self.websocket_manager.add_callback_message(client_id, message_data.get("message", ""))
                    elif message_data.get("type") == "ready":
                        # Client is ready, mark as loaded
                        if client_id in self.client_sessions:
                            self.client_sessions[client_id]["loaded"] = True

            except WebSocketDisconnect:
                self.websocket_manager.disconnect(client_id)

    def _get_chart_html(self) -> str:
        """Generate the HTML template for the chart with WebSocket integration."""
        # Read the base HTML template from lightweight-charts
        base_html_path = Path(abstract.INDEX)
        if base_html_path.exists():
            with open(base_html_path, "r") as f:
                html_content = f.read()
        else:
            # Fallback HTML if base template not found
            html_content = """
            <!DOCTYPE html>
            <html>
            <head>
                <title>TerraFin Chart</title>
                <meta charset="UTF-8">
                <meta name="viewport" content="width=device-width, initial-scale=1">
                <style>
                    body { margin: 0; padding: 0; overflow: hidden;
                           font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif; }
                </style>
            </head>
            <body>
                <div id="container"></div>
            </body>
            </html>
            """

        # Add WebSocket integration script
        websocket_script = """
        <script>
            // WebSocket integration for TerraFin Chart
            const clientId = 'client_' + Math.random().toString(36).substr(2, 9);
            const ws = new WebSocket(`ws://${window.location.host}/ws/${clientId}`);

            ws.onopen = function(event) {
                console.log('WebSocket connected');
                // Notify server that client is ready
                ws.send(JSON.stringify({type: 'ready', clientId: clientId}));
            };

            ws.onmessage = function(event) {
                const data = JSON.parse(event.data);
                if (data.type === 'execute_script') {
                    try {
                        eval(data.script);
                    } catch (error) {
                        console.error('Script execution error:', error);
                    }
                }
            };

            ws.onclose = function(event) {
                console.log('WebSocket disconnected');
            };

            // Replace the callback function to use WebSocket
            window.callbackFunction = function(message) {
                if (ws.readyState === WebSocket.OPEN) {
                    ws.send(JSON.stringify({
                        type: 'callback',
                        message: message,
                        clientId: clientId
                    }));
                }
            };
        </script>
        """

        # Insert WebSocket script before closing body tag
        html_content = html_content.replace("</body>", f"{websocket_script}</body>")
        return html_content

    def create_window(self, width, height, x=None, y=None, screen=None, on_top=False, maximize=False, title=""):
        """
        Creates a new client session. Returns a client ID.
        Note: x, y, screen, on_top, maximize parameters are ignored in web version.
        """
        client_id = str(uuid.uuid4())
        self.client_sessions[client_id] = {"width": width, "height": height, "title": title, "loaded": False}
        return client_id

    def start(self):
        """Start the web server in a separate thread."""
        if not self.server_started.is_set():
            self.server_thread = threading.Thread(target=self._run_server, daemon=True)
            self.server_thread.start()
            self.server_started.wait()  # Wait for server to start

    def _run_server(self):
        """Run the FastAPI server."""
        try:
            config = uvicorn.Config(
                self.app, host=self.host, port=self.port, log_level="error" if not self.debug else "info"
            )
            server = uvicorn.Server(config)

            # Signal that server is starting
            self.server_started.set()

            # Create new event loop for this thread
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)

            # Run the server
            loop.run_until_complete(server.serve())
        except Exception as e:
            print(f"Error starting server: {e}")
            self.server_started.set()  # Set event even on error to prevent hanging

    def show(self, client_id: str = None):
        """Open the chart in the default web browser."""
        if not self.server_started.is_set():
            self.start()

        url = f"http://{self.host}:{self.port}/"
        webbrowser.open(url)

    def hide(self, client_id: str = None):
        """Hide functionality - not applicable for web version."""
        pass  # Web version doesn't support hiding

    async def evaluate_js(self, client_id: str, script: str):
        """Send JavaScript to be executed in the browser."""
        await self.websocket_manager.send_script(client_id, script)

    def get_callback_message(self, client_id: str) -> Optional[str]:
        """Get a callback message from the client's queue."""
        if client_id in self.websocket_manager.message_queues:
            queue = self.websocket_manager.message_queues[client_id]
            if not queue.empty():
                return queue.get()
        return None

    def exit(self):
        """Clean up resources."""
        self.client_sessions.clear()
        # Note: FastAPI server cleanup would need additional implementation


class TerraFinChart(abstract.AbstractChart):
    _main_window_handlers = None
    WS: WebServerHandler = WebServerHandler()

    def __init__(
        self,
        width: int = 800,
        height: int = 600,
        x: int = None,
        y: int = None,
        title: str = "",
        screen: int = None,
        on_top: bool = False,
        maximize: bool = False,
        debug: bool = False,
        toolbox: bool = False,
        inner_width: float = 1.0,
        inner_height: float = 1.0,
        scale_candles_only: bool = False,
        position: FLOAT = "left",
    ):
        # Store debug flag and set it on the web server handler
        TerraFinChart.WS.debug = debug
        self.is_alive = True

        # Create the client session and store the client ID
        self._client_id = TerraFinChart.WS.create_window(width, height, x, y, screen, on_top, maximize, title)

        # Create the abstract window with proper script function
        chart_window = abstract.Window(
            script_func=self._execute_script_sync,
            js_api_code="window.callbackFunction",
        )

        if TerraFinChart._main_window_handlers is None:
            super().__init__(chart_window, inner_width, inner_height, scale_candles_only, toolbox, position=position)
            TerraFinChart._main_window_handlers = self.win.handlers
        else:
            chart_window.handlers = TerraFinChart._main_window_handlers
            super().__init__(chart_window, inner_width, inner_height, scale_candles_only, toolbox, position=position)

    def _execute_script_sync(self, script: str):
        """
        Synchronous wrapper for script execution.
        This is needed because the abstract.Window expects a synchronous script_func.
        """
        # Store scripts for later execution when WebSocket connection is available
        if not hasattr(self, "_pending_scripts"):
            self._pending_scripts = []
        self._pending_scripts.append(script)

        # Try to execute immediately if we have an active WebSocket connection
        if (
            self._client_id in TerraFinChart.WS.websocket_manager.active_connections
            and TerraFinChart.WS.server_started.is_set()
        ):
            try:
                # Create a new task to execute the script asynchronously
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                loop.run_until_complete(TerraFinChart.WS.evaluate_js(self._client_id, script))
                loop.close()

                # Remove from pending scripts since it was executed
                if script in self._pending_scripts:
                    self._pending_scripts.remove(script)
            except Exception as e:
                # If execution fails, keep it in pending scripts
                print(f"Failed to execute script immediately: {e}")

    async def _execute_pending_scripts(self):
        """Execute all pending scripts when WebSocket connection is established."""
        if hasattr(self, "_pending_scripts") and self._pending_scripts:
            for script in self._pending_scripts.copy():
                try:
                    await TerraFinChart.WS.evaluate_js(self._client_id, script)
                    self._pending_scripts.remove(script)
                except Exception as e:
                    print(f"Failed to execute pending script: {e}")

    def show(self, block: bool = False):
        """
        Shows the chart window by opening it in the default web browser.
        :param block: blocks execution until the chart is closed.
        """
        if not self.win.loaded:
            # Start web server and open browser
            TerraFinChart.WS.start()
            TerraFinChart.WS.show(self._client_id)

            # Mark window as loaded and execute pending scripts
            self.win.on_js_load()

            # Execute pending scripts asynchronously
            if hasattr(self, "_pending_scripts") and self._pending_scripts:
                asyncio.create_task(self._execute_pending_scripts())
        else:
            # Server already running, just open browser
            TerraFinChart.WS.show(self._client_id)

        if block:
            asyncio.run(self.show_async())

    async def show_async(self):
        """Async version of show that handles event processing."""
        self.show(block=False)
        try:
            # Handle polygon integration if available
            try:
                from lightweight_charts import polygon

                [asyncio.create_task(self.polygon.async_set(*args)) for args in polygon._set_on_load]
            except ImportError:
                pass  # polygon module not available

            while self.is_alive:
                # Check for callback messages from WebSocket
                message = TerraFinChart.WS.get_callback_message(self._client_id)
                if message:
                    if message == "exit":
                        TerraFinChart.WS.exit()
                        self.is_alive = False
                        return
                    else:
                        func, args = parse_event_message(self.win, message)
                        if asyncio.iscoroutinefunction(func):
                            await func(*args)
                        else:
                            func(*args)
                else:
                    # No messages, sleep briefly
                    await asyncio.sleep(0.05)

        except KeyboardInterrupt:
            return

    def hide(self):
        """Hides the chart window (not applicable for web version)."""
        TerraFinChart.WS.hide(self._client_id)

    def exit(self):
        """Exits and destroys the chart window."""
        TerraFinChart.WS.exit()
        self.is_alive = False
