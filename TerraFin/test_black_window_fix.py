#!/usr/bin/env python3
"""
Test script to fix the black window issue.
This creates a simple chart with sample data to verify it displays correctly.
"""

import sys
import os
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import time

# Add the src directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def create_simple_data():
    """Create very simple test data."""
    data = [
        {'time': '2024-01-01', 'open': 100, 'high': 105, 'low': 98, 'close': 102, 'volume': 1000},
        {'time': '2024-01-02', 'open': 102, 'high': 108, 'low': 101, 'close': 106, 'volume': 1200},
        {'time': '2024-01-03', 'open': 106, 'high': 110, 'low': 104, 'close': 108, 'volume': 1100},
        {'time': '2024-01-04', 'open': 108, 'high': 112, 'low': 106, 'close': 110, 'volume': 1300},
        {'time': '2024-01-05', 'open': 110, 'high': 115, 'low': 108, 'close': 113, 'volume': 1400},
    ]
    return pd.DataFrame(data)

def test_chart_display():
    """Test that the chart displays data correctly."""
    try:
        from TerraFin.visualization.lightweight_chart import TerraFinChart
        print("✓ Successfully imported TerraFinChart")
    except ImportError as e:
        print(f"✗ Import failed: {e}")
        return False
    
    try:
        # Create simple test data
        df = create_simple_data()
        print(f"✓ Created test data with {len(df)} rows")
        print("Data preview:")
        print(df.head())
        
        # Create chart with debug enabled
        print("\nCreating chart...")
        chart = TerraFinChart(
            width=1000,
            height=600,
            title="Black Window Fix Test",
            debug=True
        )
        print("✓ Chart created")
        
        # Set the data
        print("Setting data on chart...")
        chart.set(df)
        print("✓ Data set on chart")
        
        # Show the chart
        print("\n" + "="*50)
        print("OPENING CHART IN BROWSER")
        print("="*50)
        print("The chart should now display:")
        print("- 5 candlesticks from Jan 1-5, 2024")
        print("- Green/red candles showing price movement")
        print("- Volume bars at the bottom")
        print("- Interactive crosshair and zoom")
        print("\nIf you see a black window:")
        print("1. Open browser developer tools (F12)")
        print("2. Check the Console tab for errors")
        print("3. Look for 'WebSocket connected' message")
        print("4. Look for 'Basic chart created' message")
        print("\nPress Ctrl+C to stop the server")
        print("="*50)
        
        # Show chart (non-blocking first to see logs)
        chart.show(block=False)
        
        # Wait a bit for connection to establish
        print("Waiting for WebSocket connection...")
        time.sleep(3)
        
        # Now block to keep server running
        try:
            input("Press Enter to exit...")
        except KeyboardInterrupt:
            pass
        
        print("Shutting down...")
        chart.exit()
        return True
        
    except Exception as e:
        print(f"✗ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("TerraFin Chart - Black Window Fix Test")
    print("=" * 40)
    
    success = test_chart_display()
    if success:
        print("✓ Test completed")
    else:
        print("✗ Test failed")
        sys.exit(1)
