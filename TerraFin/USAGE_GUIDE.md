# TerraFin Web Chart - Usage Guide

## Quick Start

The TerraFin Chart now works without pywebview! Here's how to use it:

### Basic Usage

```python
from TerraFin.visualization import TerraFin<PERSON>hart
import pandas as pd

# Create your data (must have 'time', 'open', 'high', 'low', 'close' columns)
df = pd.DataFrame({
    'time': ['2024-01-01', '2024-01-02', '2024-01-03'],
    'open': [100, 102, 101],
    'high': [105, 106, 104],
    'low': [99, 101, 100],
    'close': [102, 101, 103],
    'volume': [1000, 1200, 900]  # optional
})

# Create and show chart
chart = TerraFinChart()
chart.set(df)
chart.show()  # Opens in your default web browser
```

### What Happens

1. **Server Starts**: A FastAPI server starts on `http://localhost:8000`
2. **Browser Opens**: Your default browser opens to the chart URL
3. **WebSocket Connects**: Real-time communication is established
4. **Chart Renders**: The lightweight-charts library renders your data

### Features

- ✅ **Interactive Charts**: Full mouse interaction (zoom, pan, crosshair)
- ✅ **Real-time Updates**: Use `chart.set(new_df)` to update data
- ✅ **Drawing Tools**: Enable with `toolbox=True`
- ✅ **Multiple Series**: Add lines, histograms, etc.
- ✅ **Callbacks**: Event handling works the same as before

### Configuration

```python
chart = TerraFinChart(
    width=1200,           # Chart width
    height=800,           # Chart height  
    title="My Chart",     # Window title
    debug=True,           # Enable debug logging
    toolbox=True          # Enable drawing tools
)
```

### Server Configuration

```python
# Access the shared server
server = TerraFinChart.WS

# Configure server settings
server.host = "localhost"  # Default: localhost only
server.port = 8000         # Default port
server.debug = True        # Enable server debug logs
```

### Troubleshooting

**Empty Chart**: 
- Check browser console (F12) for JavaScript errors
- Ensure your data has the required columns: 'time', 'open', 'high', 'low', 'close'
- Verify WebSocket connection is established (look for "WebSocket connected" in console)

**Port Already in Use**:
```python
TerraFinChart.WS.port = 8080  # Use different port
```

**Server Won't Start**:
- Check if port is available
- Install dependencies: `pip install fastapi uvicorn[standard] websockets`

### Data Format

Your DataFrame must have these columns:
- `time`: Date strings in 'YYYY-MM-DD' format
- `open`: Opening price
- `high`: Highest price  
- `low`: Lowest price
- `close`: Closing price
- `volume`: Trading volume (optional)

### Advanced Usage

```python
# Add indicators
chart.create_line('SMA_20', color='blue', width=2)

# Real-time updates
import time
for i in range(10):
    # Update your dataframe
    new_df = get_latest_data()
    chart.set(new_df)
    time.sleep(1)

# Event handling
def on_click(chart, event):
    print(f"Clicked at: {event}")

chart.events.click += on_click
```

### Migration from pywebview

If you were using the old pywebview version:

1. **No Code Changes Needed**: The API is identical
2. **Browser Instead of Window**: Charts open in browser instead of desktop window
3. **Same Features**: All functionality is preserved
4. **Better Performance**: No desktop window overhead

### Dependencies

Make sure you have installed:
```bash
pip install fastapi uvicorn[standard] websockets
```

The chart should now work perfectly in your browser!
