#!/usr/bin/env python3
"""
Debug script to identify why charts show black windows.
"""

import sys
import os
import pandas as pd

# Add the src directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def main():
    try:
        from TerraFin.visualization.lightweight_chart import TerraFinChart
        
        # Use a different port to avoid conflicts
        TerraFinChart.WS.port = 8001
        
        # Create simple test data
        df = pd.DataFrame({
            'time': ['2024-01-01', '2024-01-02', '2024-01-03'],
            'open': [100, 102, 101],
            'high': [105, 106, 104],
            'low': [99, 101, 100],
            'close': [102, 101, 103],
            'volume': [1000, 1200, 900]
        })
        
        print("Creating chart...")
        chart = TerraFinChart(debug=True)
        
        print("Setting data...")
        chart.set(df)
        
        print("Opening chart at http://localhost:8001")
        print("Check browser console (F12) for debug messages")
        print("Look for:")
        print("- 'WebSocket connected'")
        print("- 'Environment initialized'")
        print("- 'Executing script'")
        
        chart.show(block=False)
        
        input("Press Enter to exit...")
        chart.exit()
        
    except Exception as e:
        print(f"Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
