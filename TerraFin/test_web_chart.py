#!/usr/bin/env python3
"""
Quick test script for the new TerraFinChart web implementation.
"""

import sys
import os

# Add the src directory to the path so we can import TerraFin
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

try:
    from TerraFin.visualization.lightweight_chart import <PERSON><PERSON>in<PERSON>hart, WebServerHandler, WebSocketManager
    print("✓ Successfully imported TerraFinChart components")
except ImportError as e:
    print(f"✗ Import failed: {e}")
    print("Make sure you have installed the required dependencies:")
    print("pip install fastapi uvicorn[standard] websockets")
    sys.exit(1)

def test_components():
    """Test individual components."""
    print("\n=== Testing Components ===")
    
    # Test WebSocketManager
    try:
        ws_manager = WebSocketManager()
        print("✓ WebSocketManager created successfully")
    except Exception as e:
        print(f"✗ WebSocketManager failed: {e}")
        return False
    
    # Test WebServerHandler
    try:
        server_handler = WebServerHandler()
        print("✓ <PERSON>Server<PERSON>and<PERSON> created successfully")
        
        # Test client creation
        client_id = server_handler.create_window(800, 600, title="Test Chart")
        print(f"✓ Client session created: {client_id}")
        
        # Test HTML generation
        html = server_handler._get_chart_html()
        if "WebSocket" in html and "clientId" in html:
            print("✓ HTML template generated with WebSocket integration")
        else:
            print("✗ HTML template missing WebSocket integration")
            return False
            
    except Exception as e:
        print(f"✗ WebServerHandler failed: {e}")
        return False
    
    return True

def test_chart_creation():
    """Test chart creation without starting server."""
    print("\n=== Testing Chart Creation ===")
    
    try:
        chart = TerraFinChart(
            width=800,
            height=600,
            title="Test Chart",
            debug=True
        )
        print("✓ TerraFinChart created successfully")
        
        # Test that client ID was assigned
        if hasattr(chart, '_client_id') and chart._client_id:
            print(f"✓ Client ID assigned: {chart._client_id}")
        else:
            print("✗ Client ID not assigned")
            return False
            
        # Test script execution mechanism
        chart._execute_script_sync("console.log('test');")
        if hasattr(chart, '_pending_scripts') and chart._pending_scripts:
            print("✓ Script queuing mechanism working")
        else:
            print("✗ Script queuing mechanism failed")
            return False
            
    except Exception as e:
        print(f"✗ Chart creation failed: {e}")
        return False
    
    return True

def test_server_config():
    """Test server configuration."""
    print("\n=== Testing Server Configuration ===")
    
    try:
        # Test accessing the shared server handler
        server = TerraFinChart.WS
        print(f"✓ Server handler accessible")
        
        # Test configuration
        original_port = server.port
        server.port = 8080
        server.debug = True
        
        if server.port == 8080 and server.debug:
            print("✓ Server configuration working")
        else:
            print("✗ Server configuration failed")
            return False
            
        # Restore original settings
        server.port = original_port
        server.debug = False
        
    except Exception as e:
        print(f"✗ Server configuration failed: {e}")
        return False
    
    return True

def main():
    """Run all tests."""
    print("TerraFin Web Chart Test Suite")
    print("=" * 40)
    
    tests = [
        test_components,
        test_chart_creation,
        test_server_config
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
            else:
                print("Test failed!")
        except Exception as e:
            print(f"Test error: {e}")
    
    print(f"\n=== Results ===")
    print(f"Passed: {passed}/{total}")
    
    if passed == total:
        print("✓ All tests passed! The web chart implementation is ready.")
        print("\nTo test with a real chart, run:")
        print("python examples/web_chart_example.py")
    else:
        print("✗ Some tests failed. Check the errors above.")
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
