#!/usr/bin/env python3
"""
Debug what scripts are being generated and executed.
"""

import sys
import os
import pandas as pd

# Add the src directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def main():
    try:
        from TerraFin.visualization.lightweight_chart import TerraFinChart
        
        # Use a different port
        TerraFinChart.WS.port = 8002
        
        # Create simple test data
        df = pd.DataFrame({
            'time': ['2024-01-01', '2024-01-02', '2024-01-03'],
            'open': [100, 102, 101],
            'high': [105, 106, 104],
            'low': [99, 101, 100],
            'close': [102, 101, 103],
            'volume': [1000, 1200, 900]
        })
        
        print("Creating chart...")
        chart = TerraFinChart(debug=True)
        
        print("\nChart ID:", chart.id)
        print("Client ID:", chart._client_id)
        
        print("\nSetting data...")
        chart.set(df)
        
        print("\nPending scripts:")
        if hasattr(chart, '_pending_scripts'):
            for i, script in enumerate(chart._pending_scripts):
                print(f"Script {i+1}:")
                print(script[:200] + "..." if len(script) > 200 else script)
                print("-" * 50)
        
        print("\nStarting server...")
        chart.show(block=False)
        
        input("Press Enter to exit...")
        chart.exit()
        
    except Exception as e:
        print(f"Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
