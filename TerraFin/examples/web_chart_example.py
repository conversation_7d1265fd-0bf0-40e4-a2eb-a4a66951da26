#!/usr/bin/env python3
"""
Example demonstrating the new TerraFinChart with web-based interaction.
This replaces pywebview with a FastAPI + WebSocket solution.
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import asyncio

from TerraFin.visualization.lightweight_chart import TerraFinChart


def generate_sample_data(days=100):
    """Generate sample OHLCV data for testing."""
    dates = pd.date_range(start=datetime.now() - timedelta(days=days), periods=days, freq='D')
    
    # Generate realistic price data
    np.random.seed(42)
    price = 100
    prices = []
    
    for _ in range(days):
        change = np.random.normal(0, 2)  # Random price change
        price += change
        prices.append(price)
    
    # Create OHLCV data
    data = []
    for i, (date, close) in enumerate(zip(dates, prices)):
        open_price = close + np.random.normal(0, 0.5)
        high = max(open_price, close) + abs(np.random.normal(0, 1))
        low = min(open_price, close) - abs(np.random.normal(0, 1))
        volume = np.random.randint(1000, 10000)
        
        data.append({
            'time': date.strftime('%Y-%m-%d'),
            'open': round(open_price, 2),
            'high': round(high, 2),
            'low': round(low, 2),
            'close': round(close, 2),
            'volume': volume
        })
    
    return pd.DataFrame(data)


def test_basic_chart():
    """Test basic chart functionality."""
    print("Creating TerraFin Chart with web-based interaction...")
    
    # Create chart instance
    chart = TerraFinChart(
        width=1200,
        height=800,
        title="TerraFin Web Chart Example",
        debug=True,  # Enable debug mode for development
        toolbox=True  # Enable drawing tools
    )
    
    # Generate and set sample data
    df = generate_sample_data(100)
    print(f"Generated {len(df)} data points")
    
    # Set the data
    chart.set(df)
    
    # Add some indicators
    chart.create_line('SMA_20', color='blue', width=2)
    sma_data = df.copy()
    sma_data['SMA_20'] = df['close'].rolling(window=20).mean()
    chart.lines[-1].set(sma_data[['time', 'SMA_20']].dropna())
    
    # Show the chart
    print("Opening chart in web browser...")
    print("The chart will open at http://localhost:8000")
    print("Press Ctrl+C to stop the server")
    
    try:
        chart.show(block=True)
    except KeyboardInterrupt:
        print("\nShutting down...")
        chart.exit()


async def test_async_chart():
    """Test async chart functionality with real-time updates."""
    print("Creating async TerraFin Chart...")
    
    chart = TerraFinChart(
        width=1200,
        height=600,
        title="TerraFin Async Chart",
        debug=True
    )
    
    # Initial data
    df = generate_sample_data(50)
    chart.set(df)
    
    print("Opening chart in web browser...")
    chart.show(block=False)
    
    # Simulate real-time data updates
    print("Starting real-time updates (will run for 30 seconds)...")
    
    try:
        for i in range(30):
            await asyncio.sleep(1)
            
            # Add new data point
            last_price = df.iloc[-1]['close']
            new_price = last_price + np.random.normal(0, 1)
            new_date = pd.to_datetime(df.iloc[-1]['time']) + timedelta(days=1)
            
            new_row = {
                'time': new_date.strftime('%Y-%m-%d'),
                'open': last_price,
                'high': max(last_price, new_price) + abs(np.random.normal(0, 0.5)),
                'low': min(last_price, new_price) - abs(np.random.normal(0, 0.5)),
                'close': round(new_price, 2),
                'volume': np.random.randint(1000, 5000)
            }
            
            df = pd.concat([df, pd.DataFrame([new_row])], ignore_index=True)
            chart.set(df)
            
            print(f"Updated chart with new data point: {new_price:.2f}")
            
    except KeyboardInterrupt:
        print("\nStopping updates...")
    
    print("Chart will remain open. Press Ctrl+C to exit.")
    try:
        await chart.show_async()
    except KeyboardInterrupt:
        chart.exit()


if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1 and sys.argv[1] == "async":
        # Run async example
        asyncio.run(test_async_chart())
    else:
        # Run basic example
        test_basic_chart()
