# TerraFin Web Chart Architecture

## Overview

The TerraFin Chart has been redesigned to eliminate the dependency on `pywebview` while maintaining full interactivity. The new architecture uses **FastAPI + WebSocket** for real-time bidirectional communication between Python and the browser.

## Architecture Comparison

### Old Architecture (pywebview)
```
Python Code → WebviewHandler → pywebview.Window → JavaScript Runtime
                ↑                                        ↓
            Callbacks ←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←
```

### New Architecture (Web-based)
```
Python Code → WebServerHandler → FastAPI → WebSocket → Browser Client
                ↑                                         ↓
            Callbacks ←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←
```

## Key Benefits

1. **No Desktop Dependencies**: Runs in any modern web browser
2. **Universal Compatibility**: Works on any platform with a browser
3. **Remote Access**: Can be accessed from other devices on the network
4. **Better Resource Management**: No native window management overhead
5. **Web Standards**: Uses standard web technologies (WebSocket, HTML5)

## Components

### WebSocketManager
- Manages WebSocket connections between Python and browser clients
- Handles message routing and connection lifecycle
- Supports multiple concurrent clients

### WebServerHandler
- FastAPI-based web server
- Serves the chart HTML and static assets
- Provides WebSocket endpoints for real-time communication
- Manages client sessions

### TerraFinChart
- Main chart class that inherits from `AbstractChart`
- Provides the same API as the original pywebview version
- Handles script execution and event processing

## Usage

### Basic Usage
```python
from TerraFin.visualization.lightweight_chart import TerraFinChart
import pandas as pd

# Create chart
chart = TerraFinChart(
    width=1200,
    height=800,
    title="My Chart",
    debug=True
)

# Load data
df = pd.read_csv('data.csv')
chart.set(df)

# Show chart (opens in browser)
chart.show(block=True)
```

### Async Usage
```python
import asyncio

async def main():
    chart = TerraFinChart(title="Async Chart")
    chart.set(df)
    
    # Non-blocking show
    chart.show(block=False)
    
    # Handle events asynchronously
    await chart.show_async()

asyncio.run(main())
```

## Configuration

### Server Settings
The web server can be configured through the `WebServerHandler`:

```python
# Access the server handler
server = TerraFinChart.WS

# Configure host and port
server.host = "0.0.0.0"  # Allow external access
server.port = 8080       # Custom port
server.debug = True      # Enable debug logging
```

### Security Considerations
- By default, the server binds to `localhost` only
- For remote access, set `host="0.0.0.0"` but consider firewall rules
- WebSocket connections are not encrypted by default
- For production use, consider adding authentication

## Migration from pywebview

The API remains largely the same, but there are a few differences:

### What Stays the Same
- Chart creation and configuration
- Data setting with `chart.set(df)`
- Drawing tools and indicators
- Event handling and callbacks

### What Changes
- Charts now open in the default web browser instead of a native window
- The `hide()` method has no effect (browser window management)
- Server startup may take a moment on first `show()`

### Migration Steps
1. Update dependencies: `pip install fastapi uvicorn[standard] websockets`
2. Replace import: `from TerraFin.visualization.lightweight_chart import TerraFinChart`
3. No other code changes required!

## Troubleshooting

### Common Issues

**Chart doesn't open in browser**
- Check if port 8000 is available
- Try a different port: `TerraFinChart.WS.port = 8080`
- Check firewall settings

**WebSocket connection fails**
- Ensure the server is running
- Check browser console for errors
- Verify WebSocket support in browser

**Scripts not executing**
- Check browser console for JavaScript errors
- Enable debug mode: `debug=True`
- Verify WebSocket connection is established

### Debug Mode
Enable debug mode for detailed logging:
```python
chart = TerraFinChart(debug=True)
```

This will show:
- Server startup messages
- WebSocket connection events
- Script execution status
- Error messages

## Performance

The new web-based architecture offers several performance benefits:
- Reduced memory usage (no native window overhead)
- Better resource cleanup
- Improved scalability for multiple charts
- Standard browser optimizations

## Future Enhancements

Planned improvements include:
- SSL/TLS support for secure connections
- Authentication and authorization
- Multi-user support
- Chart sharing and collaboration features
- Mobile-responsive interface
